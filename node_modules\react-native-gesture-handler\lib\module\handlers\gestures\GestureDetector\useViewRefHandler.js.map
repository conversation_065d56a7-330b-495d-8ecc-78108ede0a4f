{"version": 3, "names": ["isF<PERSON><PERSON>", "tagMessage", "getShadowNodeFromRef", "useCallback", "findNodeHandle", "useViewRefHandler", "state", "updateAttachedGestures", "ref<PERSON><PERSON><PERSON>", "ref", "viewRef", "previousViewTag", "firstRender", "__DEV__", "global", "isViewFlatteningDisabled", "node", "console", "error"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useViewRefHandler.ts"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AACrD,SAASC,oBAAoB,QAAQ,+BAA+B;AAGpE,SAAgBC,WAAW,QAAQ,OAAO;AAC1C,OAAOC,cAAc,MAAM,yBAAyB;AAMpD;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAC/BC,KAA2B,EAC3BC,sBAA4D,EAC5D;EACA,MAAMC,UAAU,GAAGL,WAAW,CAC3BM,GAA2B,IAAK;IAC/B,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB;IACF;IAEAH,KAAK,CAACI,OAAO,GAAGD,GAAG;;IAEnB;IACA,IAAIH,KAAK,CAACK,eAAe,KAAK,CAAC,CAAC,EAAE;MAChCL,KAAK,CAACK,eAAe,GAAGP,cAAc,CAACE,KAAK,CAACI,OAAO,CAAW;IACjE;;IAEA;IACA;IACA,IAAI,CAACJ,KAAK,CAACM,WAAW,EAAE;MACtBL,sBAAsB,CAAC,IAAI,CAAC;IAC9B;IAEA,IAAIM,OAAO,IAAIb,QAAQ,CAAC,CAAC,IAAIc,MAAM,CAACC,wBAAwB,EAAE;MAC5D,MAAMC,IAAI,GAAGd,oBAAoB,CAACO,GAAG,CAAC;MACtC,IAAIK,MAAM,CAACC,wBAAwB,CAACC,IAAI,CAAC,KAAK,KAAK,EAAE;QACnDC,OAAO,CAACC,KAAK,CACXjB,UAAU,CACR,oEAAoE,GAClE,kGACJ,CACF,CAAC;MACH;IACF;EACF,CAAC,EACD,CAACK,KAAK,EAAEC,sBAAsB,CAChC,CAAC;EAED,OAAOC,UAAU;AACnB", "ignoreList": []}