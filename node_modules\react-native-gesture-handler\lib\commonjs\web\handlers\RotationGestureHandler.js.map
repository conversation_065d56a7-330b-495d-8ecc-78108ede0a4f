{"version": 3, "names": ["_State", "require", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "_RotationGestureDetector", "e", "__esModule", "default", "ROTATION_RECOGNITION_THRESHOLD", "Math", "PI", "RotationGestureHandler", "Gesture<PERSON>andler", "rotation", "velocity", "cachedAnchorX", "cachedAnchorY", "rotationGestureListener", "onRotationBegin", "_detector", "onRotation", "detector", "previousRotation", "delta", "<PERSON><PERSON><PERSON><PERSON>", "abs", "state", "State", "BEGAN", "activate", "onRotationEnd", "end", "rotationGestureDetector", "RotationGestureDetector", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "anchorX", "getAnchorX", "anchorY", "getAnchorY", "onPointerDown", "event", "tracker", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerMove", "trackedPointersCount", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "fail", "onPointerRemove", "UNDETERMINED", "begin", "onReset", "reset", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/RotationGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,wBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAE8C,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,8BAA8B,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE;AAEpC,MAAMC,sBAAsB,SAASC,uBAAc,CAAC;EACzDC,QAAQ,GAAG,CAAC;EACZC,QAAQ,GAAG,CAAC;EAEZC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC;EAEjBC,uBAAuB,GAA4B;IACzDC,eAAe,EAAGC,SAAkC,IAAc,IAAI;IACtEC,UAAU,EAAGC,QAAiC,IAAc;MAC1D,MAAMC,gBAAwB,GAAG,IAAI,CAACT,QAAQ;MAC9C,IAAI,CAACA,QAAQ,IAAIQ,QAAQ,CAACR,QAAQ;MAElC,MAAMU,KAAK,GAAGF,QAAQ,CAACG,SAAS;MAEhC,IAAID,KAAK,GAAG,CAAC,EAAE;QACb,IAAI,CAACT,QAAQ,GAAG,CAAC,IAAI,CAACD,QAAQ,GAAGS,gBAAgB,IAAIC,KAAK;MAC5D;MAEA,IACEd,IAAI,CAACgB,GAAG,CAAC,IAAI,CAACZ,QAAQ,CAAC,IAAIL,8BAA8B,IACzD,IAAI,CAACkB,KAAK,KAAKC,YAAK,CAACC,KAAK,EAC1B;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB;MAEA,OAAO,IAAI;IACb,CAAC;IACDC,aAAa,EAAGX,SAAkC,IAAW;MAC3D,IAAI,CAACY,GAAG,CAAC,CAAC;IACZ;EACF,CAAC;EAEOC,uBAAuB,GAC7B,IAAIC,gCAAuB,CAAC,IAAI,CAAChB,uBAAuB,CAAC;EAEpDiB,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAQ;IACjE,KAAK,CAACF,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;IAEzB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACtC;EAEUC,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACLzB,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC;MAC3C0B,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;MAC1BC,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;MAC1B5B,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG;IAC5C,CAAC;EACH;EAEO0B,UAAUA,CAAA,EAAW;IAC1B,MAAMD,OAAO,GAAG,IAAI,CAACP,uBAAuB,CAACO,OAAO;IAEpD,OAAOA,OAAO,GAAGA,OAAO,GAAG,IAAI,CAACxB,aAAa;EAC/C;EAEO2B,UAAUA,CAAA,EAAW;IAC1B,MAAMD,OAAO,GAAG,IAAI,CAACT,uBAAuB,CAACS,OAAO;IAEpD,OAAOA,OAAO,GAAGA,OAAO,GAAG,IAAI,CAACzB,aAAa;EAC/C;EAEU2B,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACG,mBAAmB,CAACH,KAAK,CAAC;EACjC;EAEUI,YAAYA,CAACJ,KAAmB,EAAQ;IAChD,IAAI,CAACC,OAAO,CAACC,YAAY,CAACF,KAAK,CAAC;IAChC,KAAK,CAACI,YAAY,CAACJ,KAAK,CAAC;IAEzB,IAAI,CAACK,QAAQ,CAAC,CAAC;IACf,IAAI,CAACjB,uBAAuB,CAACkB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;EAChE;EAEUM,aAAaA,CAACP,KAAmB,EAAQ;IACjD,IAAI,IAAI,CAACC,OAAO,CAACO,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IAEA,IAAI,IAAI,CAACZ,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACzB,aAAa,GAAG,IAAI,CAACyB,UAAU,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC0B,UAAU,CAAC,CAAC;IACxC;IAEA,IAAI,CAACG,OAAO,CAACQ,KAAK,CAACT,KAAK,CAAC;IAEzB,IAAI,CAACZ,uBAAuB,CAACkB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,KAAK,CAACM,aAAa,CAACP,KAAK,CAAC;EAC5B;EAEUU,oBAAoBA,CAACV,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAACC,OAAO,CAACO,oBAAoB,GAAG,CAAC,EAAE;MACzC;IACF;IAEA,IAAI,IAAI,CAACZ,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAACzB,aAAa,GAAG,IAAI,CAACyB,UAAU,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC0B,UAAU,CAAC,CAAC;IACxC;IAEA,IAAI,CAACG,OAAO,CAACQ,KAAK,CAACT,KAAK,CAAC;IAEzB,IAAI,CAACZ,uBAAuB,CAACkB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,KAAK,CAACS,oBAAoB,CAACV,KAAK,CAAC;EACnC;EAEUW,WAAWA,CAACX,KAAmB,EAAQ;IAC/C,KAAK,CAACW,WAAW,CAACX,KAAK,CAAC;IACxB,IAAI,CAACC,OAAO,CAACW,iBAAiB,CAACZ,KAAK,CAACa,SAAS,CAAC;IAC/C,IAAI,CAACzB,uBAAuB,CAACkB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAE9D,IAAI,IAAI,CAACnB,KAAK,KAAKC,YAAK,CAAC+B,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,IAAI,CAAChC,KAAK,KAAKC,YAAK,CAAC+B,MAAM,EAAE;MAC/B,IAAI,CAAC3B,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAAC4B,IAAI,CAAC,CAAC;IACb;EACF;EAEUC,eAAeA,CAAChB,KAAmB,EAAQ;IACnD,KAAK,CAACgB,eAAe,CAAChB,KAAK,CAAC;IAC5B,IAAI,CAACZ,uBAAuB,CAACkB,YAAY,CAACN,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC;IAC9D,IAAI,CAACA,OAAO,CAACW,iBAAiB,CAACZ,KAAK,CAACa,SAAS,CAAC;EACjD;EAEUR,QAAQA,CAAA,EAAS;IACzB,IAAI,IAAI,CAACvB,KAAK,KAAKC,YAAK,CAACkC,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEUC,OAAOA,CAAA,EAAS;IACxB,IAAI,IAAI,CAACrC,KAAK,KAAKC,YAAK,CAAC+B,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,CAAC7C,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACkB,uBAAuB,CAACgC,KAAK,CAAC,CAAC;EACtC;AACF;AAACC,OAAA,CAAA1D,OAAA,GAAAI,sBAAA", "ignoreList": []}