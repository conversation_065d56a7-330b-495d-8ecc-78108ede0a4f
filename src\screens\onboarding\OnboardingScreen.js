import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import { Colors, Typography, Spacing, ComponentStyles } from '../../utils/theme';

const { width } = Dimensions.get('window');

const onboardingData = [
  {
    id: 1,
    title: 'Track Every Walk',
    description: 'Log your pet\'s walks, map routes, and keep memories.',
    image: '🐕‍🦺', // Will be replaced with actual images
  },
  {
    id: 2,
    title: 'Discover Places',
    description: 'Find parks, cafes, and local places your furry friend will love.',
    image: '🏞️',
  },
  {
    id: 3,
    title: 'Earn Milestones',
    description: 'Celebrate achievements and unlock badges as you explore together.',
    image: '🏆',
  },
];

export default function OnboardingScreen({ navigation }) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      navigation.navigate('Login');
    }
  };

  const handleSkip = () => {
    navigation.navigate('Login');
  };

  const currentItem = onboardingData[currentIndex];

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>

      <View style={styles.content}>
        <View style={styles.imageContainer}>
          {currentIndex === 0 ? (
            <Image
              source={require('../../../assets/images/logo.png')}
              style={styles.logoImage}
              resizeMode="contain"
            />
          ) : (
            <Text style={styles.emoji}>{currentItem.image}</Text>
          )}
        </View>

        <Text style={styles.title}>{currentItem.title}</Text>
        <Text style={styles.description}>{currentItem.description}</Text>

        <View style={styles.pagination}>
          {onboardingData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                index === currentIndex ? styles.activeDot : styles.inactiveDot,
              ]}
            />
          ))}
        </View>
      </View>

      <TouchableOpacity style={styles.getStartedBtn} onPress={handleNext}>
        <Text style={styles.getStartedLabel}>
          {currentIndex === onboardingData.length - 1 ? 'Get Started' : 'Next'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary, // #FFF8E1
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xl,
  },
  skipButton: {
    alignSelf: 'flex-end',
    padding: Spacing.sm,
  },
  skipText: {
    fontSize: Typography.sizes.base,
    color: Colors.primary.gold, // #B8860B
    fontWeight: Typography.weights.medium,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },
  imageContainer: {
    width: 250,
    height: 250,
    borderRadius: 125,
    backgroundColor: 'transparent', // Remove white background
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  logoImage: {
    width: 200,
    height: 200,
  },
  emoji: {
    fontSize: 80,
  },
  title: {
    fontSize: 28, // From your design
    fontWeight: Typography.weights.bold,
    color: Colors.primary.gold, // #B8860B
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  description: {
    fontSize: 16, // From your design
    color: Colors.text.primary, // #333
    textAlign: 'center',
    lineHeight: Typography.lineHeights.relaxed * 16,
    marginBottom: Spacing.xl,
    marginTop: 12, // From your design
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24, // From your design
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: Colors.primary.gold, // #B8860B
  },
  inactiveDot: {
    backgroundColor: Colors.neutral.gray,
  },
  getStartedBtn: {
    backgroundColor: Colors.primary.gold, // #B8860B
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    position: 'absolute',
    bottom: 80,
    alignSelf: 'center',
  },
  getStartedLabel: {
    fontSize: 22, // From your design
    fontWeight: Typography.weights.bold,
    color: Colors.text.inverse, // #fff
    textAlign: 'center',
  },
});
