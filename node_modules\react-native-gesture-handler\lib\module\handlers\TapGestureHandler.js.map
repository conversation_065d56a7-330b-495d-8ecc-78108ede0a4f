{"version": 3, "names": ["createHandler", "baseGestureHandlerProps", "tapGestureHandlerProps", "tapHandlerName", "TapGestureHandler", "name", "allowedProps", "config", "shouldCancelWhenOutside"], "sourceRoot": "../../../src", "sources": ["handlers/TapGestureHandler.ts"], "mappings": ";;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;AAE/B,OAAO,MAAMC,sBAAsB,GAAG,CACpC,eAAe,EACf,YAAY,EACZ,cAAc,EACd,WAAW,EACX,WAAW,EACX,SAAS,EACT,aAAa,CACL;;AAqDV;AACA;AACA;;AAKA,OAAO,MAAMC,cAAc,GAAG,mBAAmB;;AAEjD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGJ,aAAa,CAG5C;EACAK,IAAI,EAAEF,cAAc;EACpBG,YAAY,EAAE,CACZ,GAAGL,uBAAuB,EAC1B,GAAGC,sBAAsB,CACjB;EACVK,MAAM,EAAE;IACNC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC", "ignoreList": []}