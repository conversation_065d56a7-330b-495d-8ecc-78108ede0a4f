import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, ComponentStyles } from '../../utils/theme';

const { width } = Dimensions.get('window');

export default function LoginScreen({ navigation }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    // TODO: Implement actual login logic with Supabase
    setTimeout(() => {
      setLoading(false);
      Alert.alert('Success', 'Login functionality will be implemented next!');
    }, 1000);
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    // TODO: Implement Google OAuth login
    setTimeout(() => {
      setLoading(false);
      Alert.alert('Info', 'Google login will be implemented next!');
    }, 1000);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* Logo Section */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../../assets/images/logo.png')}
            style={styles.logoImage}
            resizeMode="contain"
          />
          <Text style={styles.logoTitle}>PawGo</Text>
          <Text style={styles.welcomeText}>Welcome back, friend! 🐾</Text>
        </View>

        {/* Form Section */}
        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <Ionicons 
              name="mail-outline" 
              size={20} 
              color={Colors.neutral.mediumGray} 
              style={styles.inputIcon}
            />
            <TextInput
              style={styles.input}
              placeholder="Email or username"
              placeholderTextColor={Colors.neutral.mediumGray}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputContainer}>
            <Ionicons 
              name="lock-closed-outline" 
              size={20} 
              color={Colors.neutral.mediumGray} 
              style={styles.inputIcon}
            />
            <TextInput
              style={styles.input}
              placeholder="Password"
              placeholderTextColor={Colors.neutral.mediumGray}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity 
              onPress={() => setShowPassword(!showPassword)}
              style={styles.eyeIcon}
            >
              <Ionicons 
                name={showPassword ? "eye-outline" : "eye-off-outline"} 
                size={20} 
                color={Colors.neutral.mediumGray} 
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.forgotPassword}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>Forgot password?</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.getStartedScreenBtn, loading && styles.disabledButton]}
            onPress={handleLogin}
            disabled={loading}
          >
            <Text style={styles.getStartedScreenLabel}>
              {loading ? 'Signing in...' : 'Log In'}
            </Text>
          </TouchableOpacity>

          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.dividerLine} />
          </View>

          <TouchableOpacity
            style={styles.getStartedScreenBtnOutlined}
            onPress={handleGoogleLogin}
            disabled={loading}
          >
            <View style={styles.googleButtonContent}>
              <Image
                source={{ uri: 'https://developers.google.com/identity/images/g-logo.png' }}
                style={styles.googleLogo}
                resizeMode="contain"
              />
              <Text style={styles.getStartedScreenLabelOutlined}>Continue with Google</Text>
            </View>
          </TouchableOpacity>

          <View style={styles.signupContainer}>
            <Text style={styles.signupText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Signup')}>
              <Text style={styles.signupLink}>Sign up instead</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary, // #FFF8E1
  },
  content: {
    padding: 32, // From your design
    minHeight: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 32, // From your design
  },
  logoImage: {
    width: 200, // Slightly smaller for login screen
    height: 200,
    marginBottom: 40, // From your design
    backgroundColor: 'transparent',
  },
  logoTitle: {
    fontSize: 32, // From your design
    fontWeight: Typography.weights.bold,
    color: Colors.primary.gold, // #B8860B
    marginBottom: Spacing.xs,
  },
  welcomeText: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
  },
  formContainer: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: Typography.sizes.base,
    color: Colors.text.primary,
  },
  eyeIcon: {
    padding: Spacing.xs,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24, // From your design
  },
  forgotPasswordText: {
    fontSize: Typography.sizes.sm,
    color: Colors.primary.gold, // #B8860B
  },
  getStartedScreenBtn: {
    backgroundColor: Colors.primary.gold, // #B8860B
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    marginBottom: 24, // From your design
  },
  disabledButton: {
    opacity: 0.6,
  },
  getStartedScreenLabel: {
    fontSize: 22, // From your design
    fontWeight: Typography.weights.bold,
    color: Colors.text.inverse, // #fff
    textAlign: 'center',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24, // From your design
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.border.light,
  },
  dividerText: {
    marginHorizontal: Spacing.md,
    fontSize: Typography.sizes.sm,
    color: Colors.text.tertiary,
  },
  getStartedScreenBtnOutlined: {
    borderColor: Colors.primary.gold, // #B8860B
    borderWidth: 2,
    backgroundColor: Colors.background.secondary, // #FFF8E1
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    marginBottom: 24, // From your design
  },
  getStartedScreenLabelOutlined: {
    fontSize: 22, // From your design
    fontWeight: Typography.weights.bold,
    color: Colors.primary.gold, // #B8860B
    textAlign: 'center',
  },
  googleButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  googleLogo: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  signupContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupText: {
    fontSize: Typography.sizes.base,
    color: Colors.text.secondary,
  },
  signupLink: {
    fontSize: Typography.sizes.base,
    color: Colors.primary.gold, // #B8860B
    fontWeight: Typography.weights.medium,
  },
});
