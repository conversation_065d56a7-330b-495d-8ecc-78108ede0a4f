/**
 * PawGo Theme System
 * Golden Retriever-inspired color palette with warm golds, creams, browns, and teal highlights
 */

export const Colors = {
  // Primary Golden Colors (from your design)
  primary: {
    gold: '#B8860B',        // Main golden color (Dark Goldenrod)
    lightGold: '#DAA520',   // Lighter golden shade (Goldenrod)
    darkGold: '#9A7209',    // Darker golden shade
    cream: '#FFF8E1',       // Light cream background
    warmCream: '#FFFACD',   // Warmer cream tone (Lemon Chiffon)
  },

  // Secondary Brown Tones
  secondary: {
    lightBrown: '#D2B48C',  // Tan
    mediumBrown: '#CD853F', // Peru
    darkBrown: '#8B4513',   // Saddle Brown
    chocolate: '#654321',   // Dark Brown
  },

  // Accent Colors
  accent: {
    teal: '#4A9B8E',        // Keep teal for variety
    lightTeal: '#6BB5A7',   // Lighter teal
    darkTeal: '#3A7B70',    // Darker teal
    mintTeal: '#7FCCC0',    // Mint teal for highlights
  },
  
  // Neutral Colors
  neutral: {
    white: '#FFFFFF',
    lightGray: '#F8F8F8',
    gray: '#E5E5E5',
    mediumGray: '#B0B0B0',
    darkGray: '#6B6B6B',
    charcoal: '#333333',    // Updated to match your design
    black: '#1A1A1A',
  },

  // Status Colors
  status: {
    success: '#4A9B8E',     // Using teal for success
    warning: '#B8860B',     // Using main gold for warning
    error: '#D67B7B',       // Soft red that complements the palette
    info: '#7FCCC0',        // Using mint teal for info
  },

  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#FFF8E1',   // Light cream background (from your design)
    tertiary: '#FFFACD',    // Warm cream background
    card: '#FFFFFF',
    overlay: 'rgba(26, 26, 26, 0.5)',
  },

  // Text Colors
  text: {
    primary: '#333333',     // Main text color (from your design)
    secondary: '#654321',   // Secondary text (dark brown)
    tertiary: '#6B6B6B',    // Tertiary text (gray)
    inverse: '#FFFFFF',     // White text for dark backgrounds
    accent: '#B8860B',      // Gold text for accents
    muted: '#B0B0B0',       // Muted text
  },
  
  // Border Colors
  border: {
    light: '#E5E5E5',
    medium: '#B0B0B0',
    dark: '#6B6B6B',
    accent: '#4A9B8E',
  },
};

export const Typography = {
  // Font Families
  fonts: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
    light: 'System',
  },
  
  // Font Sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // Line Heights
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Font Weights
  weights: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

export const Shadows = {
  sm: {
    shadowColor: Colors.neutral.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: Colors.neutral.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: Colors.neutral.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

export const Layout = {
  // Screen padding
  screenPadding: Spacing.md,
  
  // Component spacing
  componentSpacing: Spacing.lg,
  
  // Button heights
  buttonHeight: {
    sm: 36,
    md: 48,
    lg: 56,
  },
  
  // Input heights
  inputHeight: {
    sm: 36,
    md: 48,
    lg: 56,
  },
  
  // Header height
  headerHeight: 60,
  
  // Tab bar height
  tabBarHeight: 80,
};

// Pre-defined component styles
export const ComponentStyles = {
  // Button styles
  button: {
    primary: {
      backgroundColor: Colors.primary.gold, // #B8860B
      borderRadius: BorderRadius.lg,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      ...Shadows.sm,
    },
    secondary: {
      backgroundColor: Colors.accent.teal,
      borderRadius: BorderRadius.lg,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      ...Shadows.sm,
    },
    outline: {
      backgroundColor: Colors.background.secondary, // #FFF8E1
      borderWidth: 2,
      borderColor: Colors.primary.gold, // #B8860B
      borderRadius: BorderRadius.lg,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
    },
  },
  
  // Card styles
  card: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    ...Shadows.md,
  },
  
  // Input styles
  input: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
    fontSize: Typography.sizes.base,
    color: Colors.text.primary,
  },
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  ComponentStyles,
};
