{"version": 3, "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "rotationChange", "rotation", "RotationGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/rotationGesture.ts"], "mappings": ";;AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAQhD,SAASC,qBAAqBA,CAC5BC,OAA+D,EAC/DC,QAAiE,EACjE;EACA,SAAS;;EACT,IAAIC,aAAgD;EACpD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,cAAc,EAAEJ,OAAO,CAACK;IAC1B,CAAC;EACH,CAAC,MAAM;IACLH,aAAa,GAAG;MACdE,cAAc,EAAEJ,OAAO,CAACK,QAAQ,GAAGJ,QAAQ,CAACI;IAC9C,CAAC;EACH;EAEA,OAAO;IAAE,GAAGL,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEA,OAAO,MAAMI,eAAe,SAASR,oBAAoB,CAGvD;EACAS,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,wBAAwB;EAC7C;EAEAC,QAAQA,CACNC,QAIS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACZ,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF", "ignoreList": []}