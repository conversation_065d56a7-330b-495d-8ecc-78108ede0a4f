{"version": 3, "names": ["_createHandler", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "tapGestureHandlerProps", "exports", "tapHandlerName", "TapGestureHandler", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config", "shouldCancelWhenOutside"], "sourceRoot": "../../../src", "sources": ["handlers/TapGestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGgC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEzB,MAAMG,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,GAAG,CACpC,eAAe,EACf,YAAY,EACZ,cAAc,EACd,WAAW,EACX,WAAW,EACX,SAAS,EACT,aAAa,CACL;;AAqDV;AACA;AACA;;AAKO,MAAME,cAAc,GAAAD,OAAA,CAAAC,cAAA,GAAG,mBAAmB;;AAEjD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,MAAMC,iBAAiB,GAAAF,OAAA,CAAAE,iBAAA,GAAG,IAAAC,sBAAa,EAG5C;EACAC,IAAI,EAAEH,cAAc;EACpBI,YAAY,EAAE,CACZ,GAAGC,6CAAuB,EAC1B,GAAGP,sBAAsB,CACjB;EACVQ,MAAM,EAAE;IACNC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC", "ignoreList": []}