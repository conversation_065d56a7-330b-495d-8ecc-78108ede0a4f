{"version": 3, "names": ["_gesture", "require", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "rotationChange", "rotation", "RotationGesture", "ContinousBaseGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/rotationGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAQA,SAASC,qBAAqBA,CAC5BC,OAA+D,EAC/DC,QAAiE,EACjE;EACA,SAAS;;EACT,IAAIC,aAAgD;EACpD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,cAAc,EAAEJ,OAAO,CAACK;IAC1B,CAAC;EACH,CAAC,MAAM;IACLH,aAAa,GAAG;MACdE,cAAc,EAAEJ,OAAO,CAACK,QAAQ,GAAGJ,QAAQ,CAACI;IAC9C,CAAC;EACH;EAEA,OAAO;IAAE,GAAGL,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEO,MAAMI,eAAe,SAASC,6BAAoB,CAGvD;EACAC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,wBAAwB;EAC7C;EAEAC,QAAQA,CACNC,QAIS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACb,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACW,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF;AAACE,OAAA,CAAAP,eAAA,GAAAA,eAAA", "ignoreList": []}