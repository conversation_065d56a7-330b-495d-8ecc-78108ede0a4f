{"version": 3, "names": ["_gesture", "require", "_reanimated<PERSON><PERSON>per", "_gestureStateManager", "_State", "_TouchEventType", "_utils", "<PERSON><PERSON><PERSON><PERSON>", "type", "gesture", "CALLBACK_TYPE", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "eventType", "TouchEventType", "UNDEFINED", "runWorklet", "event", "args", "handler", "isWorklet", "console", "warn", "tagMessage", "isStateChangeEvent", "oldState", "isTouchEvent", "useAnimatedGesture", "preparedGesture", "needsRebuild", "Reanimated", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "value", "i", "length", "handlerTag", "State", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "GestureStateManager", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "animatedHandlers"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useAnimatedGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAMA,IAAAE,oBAAA,GAAAF,OAAA;AAIA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAGA,SAASM,UAAUA,CACjBC,IAAmB,EACnBC,OAAkD,EAClD;EACA,SAAS;;EACT,QAAQD,IAAI;IACV,KAAKE,sBAAa,CAACC,KAAK;MACtB,OAAOF,OAAO,CAACG,OAAO;IACxB,KAAKF,sBAAa,CAACG,KAAK;MACtB,OAAOJ,OAAO,CAACK,OAAO;IACxB,KAAKJ,sBAAa,CAACK,MAAM;MACvB,OAAON,OAAO,CAACO,QAAQ;IACzB,KAAKN,sBAAa,CAACO,MAAM;MACvB,OAAOR,OAAO,CAACS,QAAQ;IACzB,KAAKR,sBAAa,CAACS,GAAG;MACpB,OAAOV,OAAO,CAACW,KAAK;IACtB,KAAKV,sBAAa,CAACW,QAAQ;MACzB,OAAOZ,OAAO,CAACa,UAAU;IAC3B,KAAKZ,sBAAa,CAACa,YAAY;MAC7B,OAAOd,OAAO,CAACe,aAAa;IAC9B,KAAKd,sBAAa,CAACe,YAAY;MAC7B,OAAOhB,OAAO,CAACiB,aAAa;IAC9B,KAAKhB,sBAAa,CAACiB,UAAU;MAC3B,OAAOlB,OAAO,CAACmB,WAAW;IAC5B,KAAKlB,sBAAa,CAACmB,iBAAiB;MAClC,OAAOpB,OAAO,CAACqB,kBAAkB;EACrC;AACF;AAEA,SAASC,4BAA4BA,CACnCC,SAAyB,EACV;EACf,SAAS;;EACT,QAAQA,SAAS;IACf,KAAKC,8BAAc,CAACV,YAAY;MAC9B,OAAOb,sBAAa,CAACa,YAAY;IACnC,KAAKU,8BAAc,CAACR,YAAY;MAC9B,OAAOf,sBAAa,CAACe,YAAY;IACnC,KAAKQ,8BAAc,CAACN,UAAU;MAC5B,OAAOjB,sBAAa,CAACiB,UAAU;IACjC,KAAKM,8BAAc,CAACJ,iBAAiB;MACnC,OAAOnB,sBAAa,CAACmB,iBAAiB;EAC1C;EACA,OAAOnB,sBAAa,CAACwB,SAAS;AAChC;AAEA,SAASC,UAAUA,CACjB3B,IAAmB,EACnBC,OAAkD,EAClD2B,KAAuE,EACvE,GAAGC,IAAe,EAClB;EACA,SAAS;;EACT,MAAMC,OAAO,GAAG/B,UAAU,CAACC,IAAI,EAAEC,OAAO,CAAC;EACzC,IAAIA,OAAO,CAAC8B,SAAS,CAAC/B,IAAI,CAAC,EAAE;IAC3B;IACA;IACA8B,OAAO,GAAGF,KAAK,EAAE,GAAGC,IAAI,CAAC;EAC3B,CAAC,MAAM,IAAIC,OAAO,EAAE;IAClBE,OAAO,CAACC,IAAI,CAAC,IAAAC,iBAAU,EAAC,6CAA6C,CAAC,CAAC;EACzE;AACF;AAEA,SAASC,kBAAkBA,CACzBP,KAAuE,EACrC;EAClC,SAAS;;EACT;EACA,OAAOA,KAAK,CAACQ,QAAQ,IAAI,IAAI;AAC/B;AAEA,SAASC,YAAYA,CACnBT,KAAuE,EAC3C;EAC5B,SAAS;;EACT,OAAOA,KAAK,CAACJ,SAAS,IAAI,IAAI;AAChC;AAEO,SAASc,kBAAkBA,CAChCC,eAAqC,EACrCC,YAAqB,EACrB;EACA,IAAI,CAACC,6BAAU,EAAE;IACf;EACF;;EAEA;EACA;EACA;EACA,MAAMC,uBAAuB,GAAGD,6BAAU,CAACE,cAAc,CAEvD,IAAI,CAAC;;EAEP;EACA,MAAMC,eAAe,GAAGH,6BAAU,CAACE,cAAc,CAE/C,EAAE,CAAC;;EAEL;EACA,MAAME,gBAA2C,GAAG,EAAE;EAEtD,MAAMC,QAAQ,GACZlB,KAAuE,IACpE;IACH,SAAS;;IAET,MAAMmB,eAAe,GAAGL,uBAAuB,CAACM,KAAK;IACrD,IAAI,CAACD,eAAe,EAAE;MACpB;IACF;IAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAMhD,OAAO,GAAG8C,eAAe,CAACE,CAAC,CAAC;MAElC,IAAIrB,KAAK,CAACuB,UAAU,KAAKlD,OAAO,CAACkD,UAAU,EAAE;QAC3C;MACF;MAEA,IAAIhB,kBAAkB,CAACP,KAAK,CAAC,EAAE;QAC7B,IACEA,KAAK,CAACQ,QAAQ,KAAKgB,YAAK,CAACC,YAAY,IACrCzB,KAAK,CAAC0B,KAAK,KAAKF,YAAK,CAACjD,KAAK,EAC3B;UACAwB,UAAU,CAACzB,sBAAa,CAACC,KAAK,EAAEF,OAAO,EAAE2B,KAAK,CAAC;QACjD,CAAC,MAAM,IACL,CAACA,KAAK,CAACQ,QAAQ,KAAKgB,YAAK,CAACjD,KAAK,IAC7ByB,KAAK,CAACQ,QAAQ,KAAKgB,YAAK,CAACC,YAAY,KACvCzB,KAAK,CAAC0B,KAAK,KAAKF,YAAK,CAACG,MAAM,EAC5B;UACA5B,UAAU,CAACzB,sBAAa,CAACG,KAAK,EAAEJ,OAAO,EAAE2B,KAAK,CAAC;UAC/CgB,eAAe,CAACI,KAAK,CAAC/C,OAAO,CAACkD,UAAU,CAAC,GAAGK,SAAS;QACvD,CAAC,MAAM,IACL5B,KAAK,CAACQ,QAAQ,KAAKR,KAAK,CAAC0B,KAAK,IAC9B1B,KAAK,CAAC0B,KAAK,KAAKF,YAAK,CAACzC,GAAG,EACzB;UACA,IAAIiB,KAAK,CAACQ,QAAQ,KAAKgB,YAAK,CAACG,MAAM,EAAE;YACnC5B,UAAU,CAACzB,sBAAa,CAACS,GAAG,EAAEV,OAAO,EAAE2B,KAAK,EAAE,IAAI,CAAC;UACrD;UACAD,UAAU,CAACzB,sBAAa,CAACW,QAAQ,EAAEZ,OAAO,EAAE2B,KAAK,EAAE,IAAI,CAAC;QAC1D,CAAC,MAAM,IACL,CAACA,KAAK,CAAC0B,KAAK,KAAKF,YAAK,CAACK,MAAM,IAAI7B,KAAK,CAAC0B,KAAK,KAAKF,YAAK,CAACM,SAAS,KAChE9B,KAAK,CAAC0B,KAAK,KAAK1B,KAAK,CAACQ,QAAQ,EAC9B;UACA,IAAIR,KAAK,CAACQ,QAAQ,KAAKgB,YAAK,CAACG,MAAM,EAAE;YACnC5B,UAAU,CAACzB,sBAAa,CAACS,GAAG,EAAEV,OAAO,EAAE2B,KAAK,EAAE,KAAK,CAAC;UACtD;UACAD,UAAU,CAACzB,sBAAa,CAACW,QAAQ,EAAEZ,OAAO,EAAE2B,KAAK,EAAE,KAAK,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIS,YAAY,CAACT,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACiB,gBAAgB,CAACI,CAAC,CAAC,EAAE;UACxBJ,gBAAgB,CAACI,CAAC,CAAC,GAAGU,wCAAmB,CAACC,MAAM,CAAChC,KAAK,CAACuB,UAAU,CAAC;QACpE;QAEA,IAAIvB,KAAK,CAACJ,SAAS,KAAKC,8BAAc,CAAC4B,YAAY,EAAE;UACnD1B,UAAU,CACRJ,4BAA4B,CAACK,KAAK,CAACJ,SAAS,CAAC,EAC7CvB,OAAO,EACP2B,KAAK,EACLiB,gBAAgB,CAACI,CAAC,CACpB,CAAC;QACH;MACF,CAAC,MAAM;QACLtB,UAAU,CAACzB,sBAAa,CAACK,MAAM,EAAEN,OAAO,EAAE2B,KAAK,CAAC;QAEhD,IAAI3B,OAAO,CAACS,QAAQ,IAAIT,OAAO,CAAC4D,qBAAqB,EAAE;UACrDlC,UAAU,CACRzB,sBAAa,CAACO,MAAM,EACpBR,OAAO,EACPA,OAAO,CAAC4D,qBAAqB,GAC3BjC,KAAK,EACLgB,eAAe,CAACI,KAAK,CAAC/C,OAAO,CAACkD,UAAU,CAC1C,CACF,CAAC;UAEDP,eAAe,CAACI,KAAK,CAAC/C,OAAO,CAACkD,UAAU,CAAC,GAAGvB,KAAK;QACnD;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMA,KAAK,GAAGa,6BAAU,CAACqB,QAAQ,CAC/BhB,QAAQ,EACR,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,EACxDN,YACF,CAAC;EAEDD,eAAe,CAACwB,oBAAoB,GAAGnC,KAAK;EAC5CW,eAAe,CAACyB,gBAAgB,GAAGtB,uBAAuB;AAC5D", "ignoreList": []}