{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_DiscreteGestureHandler", "_utils", "e", "__esModule", "default", "TapGestureHandler", "DiscreteGestureHandler", "_shouldFireEndEvent", "name", "NativeGestureClass", "Hammer", "Tap", "max<PERSON>elay<PERSON>", "isnan", "config", "simulateCancelEvent", "inputData", "isGestureRunning", "cancelEvent", "onGestureActivated", "ev", "onSuccessfulTap", "_getPendingGestures", "length", "eventType", "INPUT_END", "sendEvent", "INPUT_MOVE", "isFinal", "onGestureEnded", "onRawEvent", "hasGestureFailed", "gesture", "hammer", "get", "options", "enable", "clearTimeout", "_multiTapTimer", "onStart", "maxPointers", "setTimeout", "_timer", "getHammerConfig", "event", "taps", "numberOfTaps", "interval", "time", "maxDurationMs", "updateGestureConfig", "shouldCancelWhenOutside", "maxDeltaX", "Number", "NaN", "maxDeltaY", "minDurationMs", "maxDist", "minPointers", "props", "onWaitingEnded", "_gesture", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/TapGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAAgC,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhC,MAAMG,iBAAiB,SAASC,+BAAsB,CAAC;EAC7CC,mBAAmB,GAA0B,IAAI;EAE5B;EAC7B,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,KAAK;EACd;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,GAAG;EACnB;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf;IACA,OAAO,IAAAC,YAAK,EAAC,IAAI,CAACC,MAAM,CAACF,UAAU,CAAC,GAAG,GAAG,GAAG,IAAI,CAACE,MAAM,CAACF,UAAU;EACrE;EAEAG,mBAAmBA,CAACC,SAAyB,EAAE;IAC7C,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,WAAW,CAACF,SAAS,CAAC;IAC7B;EACF;EAEAG,kBAAkBA,CAACC,EAAkB,EAAE;IACrC,IAAI,IAAI,CAACH,gBAAgB,EAAE;MACzB,IAAI,CAACI,eAAe,CAACD,EAAE,CAAC;IAC1B;EACF;EAEAC,eAAe,GAAID,EAAkB,IAAK;IACxC,IAAI,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAACC,MAAM,EAAE;MACrC,IAAI,CAAChB,mBAAmB,GAAGa,EAAE;MAC7B;IACF;IACA,IAAIA,EAAE,CAACI,SAAS,KAAKd,iBAAM,CAACe,SAAS,EAAE;MACrC,IAAI,CAACC,SAAS,CAAC;QAAE,GAAGN,EAAE;QAAEI,SAAS,EAAEd,iBAAM,CAACiB;MAAW,CAAC,CAAC;IACzD;IACA;IACA,IAAI,CAACD,SAAS,CAAC;MAAE,GAAGN,EAAE;MAAEQ,OAAO,EAAE;IAAK,CAAC,CAAC;IACxC,IAAI,CAACC,cAAc,CAACT,EAAE,CAAC;EACzB,CAAC;EAEDU,UAAUA,CAACV,EAAe,EAAE;IAC1B,KAAK,CAACU,UAAU,CAACV,EAAE,CAAC;;IAEpB;IACA,IACE,CAAC,IAAI,CAACW,gBAAgB,IACtB,CAAC,IAAI,CAACd,gBAAgB;IACtB;IACA,CAACG,EAAE,CAACQ,OAAO,EACX;MACA;MACA,MAAMI,OAAO,GAAG,IAAI,CAACC,MAAM,CAAEC,GAAG,CAAC,IAAI,CAAC1B,IAAI,CAAC;MAC3C;MACA,IAAIwB,OAAO,CAACG,OAAO,CAACC,MAAM,CAACJ,OAAO,EAAEZ,EAAE,CAAC,EAAE;QACvCiB,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;QAEjC,IAAI,CAACC,OAAO,CAACnB,EAAE,CAAC;QAChB,IAAI,CAACM,SAAS,CAACN,EAAE,CAAC;MACpB;IACF;IACA,IAAIA,EAAE,CAACQ,OAAO,IAAIR,EAAE,CAACoB,WAAW,GAAG,CAAC,EAAE;MACpCC,UAAU,CAAC,MAAM;QACf;QACA;QACA,IAAI,IAAI,CAACxB,gBAAgB,EAAE;UACzB,IAAI,CAACC,WAAW,CAACE,EAAE,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,IAAI,CAACW,gBAAgB,EAAE;MACzB;IACF;IACA;IACA;IACA,IAAIX,EAAE,CAACQ,OAAO,EAAE;MACd;MACA;MACA,IAAIR,EAAE,CAACoB,WAAW,GAAG,CAAC,EAAE;QACtBC,UAAU,CAAC,MAAM;UACf,IAAI,IAAI,CAACxB,gBAAgB,EAAE;YACzB,IAAI,CAACC,WAAW,CAACE,EAAE,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;;MAEA;MACAiB,YAAY,CAAC,IAAI,CAACK,MAAM,CAAC;MACzB;MACA,IAAI,CAACA,MAAM,GAAGD,UAAU,CAAC,MAAM;QAC7B,IAAI,CAACV,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACb,WAAW,CAACE,EAAE,CAAC;MACtB,CAAC,EAAE,IAAI,CAACR,UAAU,CAAC;IACrB,CAAC,MAAM,IAAI,CAAC,IAAI,CAACmB,gBAAgB,IAAI,CAAC,IAAI,CAACd,gBAAgB,EAAE;MAC3D;MACA,MAAMe,OAAO,GAAG,IAAI,CAACC,MAAM,CAAEC,GAAG,CAAC,IAAI,CAAC1B,IAAI,CAAC;MAC3C;MACA,IAAIwB,OAAO,CAACG,OAAO,CAACC,MAAM,CAACJ,OAAO,EAAEZ,EAAE,CAAC,EAAE;QACvCiB,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;QAEjC,IAAI,CAACC,OAAO,CAACnB,EAAE,CAAC;QAChB,IAAI,CAACM,SAAS,CAACN,EAAE,CAAC;MACpB;IACF;EACF;EAEAuB,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1BC,KAAK,EAAE,IAAI,CAACpC,IAAI;MAChB;MACAqC,IAAI,EAAE,IAAAhC,YAAK,EAAC,IAAI,CAACC,MAAM,CAACgC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAChC,MAAM,CAACgC,YAAY;MACpEC,QAAQ,EAAE,IAAI,CAACnC,UAAU;MACzBoC,IAAI;MACF;MACA,IAAAnC,YAAK,EAAC,IAAI,CAACC,MAAM,CAACmC,aAAa,CAAC,IAAI,IAAI,CAACnC,MAAM,CAACmC,aAAa,IAAI,IAAI,GACjE,GAAG;MACH;MACA,IAAI,CAACnC,MAAM,CAACmC;IACpB,CAAC;EACH;EAEAC,mBAAmBA,CAAC;IAClBC,uBAAuB,GAAG,IAAI;IAC9BC,SAAS,GAAGC,MAAM,CAACC,GAAG;IACtBC,SAAS,GAAGF,MAAM,CAACC,GAAG;IACtBR,YAAY,GAAG,CAAC;IAChBU,aAAa,GAAG,GAAG;IACnB5C,UAAU,GAAGyC,MAAM,CAACC,GAAG;IACvB;IACAL,aAAa,GAAGI,MAAM,CAACC,GAAG;IAC1BG,OAAO,GAAG,CAAC;IACXC,WAAW,GAAG,CAAC;IACflB,WAAW,GAAG,CAAC;IACf,GAAGmB;EACL,CAAC,EAAE;IACD,OAAO,KAAK,CAACT,mBAAmB,CAAC;MAC/BC,uBAAuB;MACvBL,YAAY;MACZM,SAAS;MACTG,SAAS;MACTC,aAAa;MACb5C,UAAU;MACV6C,OAAO;MACPC,WAAW;MACXlB,WAAW;MACX,GAAGmB;IACL,CAAC,CAAC;EACJ;EAEA9B,cAAcA,CAAC,GAAG8B,KAAU,EAAE;IAC5BtB,YAAY,CAAC,IAAI,CAACK,MAAM,CAAC;IACzB;IACA,KAAK,CAACb,cAAc,CAAC,GAAG8B,KAAK,CAAC;EAChC;EAEAC,cAAcA,CAACC,QAAa,EAAE;IAC5B,IAAI,IAAI,CAACtD,mBAAmB,EAAE;MAC5B,IAAI,CAACc,eAAe,CAAC,IAAI,CAACd,mBAAmB,CAAC;MAC9C,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACjC;EACF;AACF;AAAC,IAAAuD,QAAA,GAAAC,OAAA,CAAA3D,OAAA,GACcC,iBAAiB", "ignoreList": []}